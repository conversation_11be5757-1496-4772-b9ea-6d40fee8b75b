const { PrismaClient } = require('@prisma/client');
const logger = require('../utils/logger');

const prisma = new PrismaClient();

/**
 * Exchange Database Service
 * Handles all database operations for exchange deposit/withdraw data
 */
class ExchangeDatabaseService {
  constructor() {
    this.prisma = prisma;
  }

  /**
   * Get or create exchange record
   */
  async getOrCreateExchange(exchangeData) {
    try {
      return await this.prisma.exchange.upsert({
        where: { name: exchangeData.exchange },
        update: {
          display_name: exchangeData.exchange,
          updated_at: new Date()
        },
        create: {
          name: exchangeData.exchange,
          display_name: exchangeData.exchange,
          api_base_url: this.getExchangeApiUrl(exchangeData.exchange)
        }
      });
    } catch (error) {
      logger.error(`Failed to get/create exchange ${exchangeData.exchange}:`, error.message);
      throw error;
    }
  }

  /**
   * Get or create token record
   */
  async getOrCreateToken(tokenSymbol) {
    try {
      return await this.prisma.token.upsert({
        where: { symbol: tokenSymbol },
        update: {
          updated_at: new Date()
        },
        create: {
          symbol: tokenSymbol,
          name: this.getTokenName(tokenSymbol)
        }
      });
    } catch (error) {
      logger.error(`Failed to get/create token ${tokenSymbol}:`, error.message);
      throw error;
    }
  }

  /**
   * Get or create network record
   */
  async getOrCreateNetwork(networkName) {
    try {
      return await this.prisma.network.upsert({
        where: { name: networkName },
        update: {
          updated_at: new Date()
        },
        create: {
          name: networkName,
          display_name: this.getNetworkDisplayName(networkName),
          chain_id: this.getNetworkChainId(networkName)
        }
      });
    } catch (error) {
      logger.error(`Failed to get/create network ${networkName}:`, error.message);
      throw error;
    }
  }

  /**
   * Save or update deposit/withdraw record
   */
  async saveDepositWithdrawRecord(recordData) {
    try {
      // Get or create related entities
      const exchange = await this.getOrCreateExchange(recordData);
      const token = await this.getOrCreateToken(recordData.token);
      const network = await this.getOrCreateNetwork(recordData.network);

      // Upsert the deposit/withdraw record
      return await this.prisma.depositWithdrawRecord.upsert({
        where: {
          exchange_id_token_id_network_id: {
            exchange_id: exchange.id,
            token_id: token.id,
            network_id: network.id
          }
        },
        update: {
          can_deposit: recordData.can_deposit,
          can_withdraw: recordData.can_withdraw,
          deposit_fee: recordData.deposit_fee,
          withdrawal_fee: recordData.withdrawal_fee,
          min_deposit: recordData.min_deposit,
          min_withdrawal: recordData.min_withdrawal,
          deposit_confirmations: recordData.deposit_confirmations,
          withdrawal_confirmations: recordData.withdrawal_confirmations,
          status_notes: recordData.status_notes,
          last_updated_at: new Date()
        },
        create: {
          exchange_id: exchange.id,
          token_id: token.id,
          network_id: network.id,
          can_deposit: recordData.can_deposit,
          can_withdraw: recordData.can_withdraw,
          deposit_fee: recordData.deposit_fee,
          withdrawal_fee: recordData.withdrawal_fee,
          min_deposit: recordData.min_deposit,
          min_withdrawal: recordData.min_withdrawal,
          deposit_confirmations: recordData.deposit_confirmations,
          withdrawal_confirmations: recordData.withdrawal_confirmations,
          status_notes: recordData.status_notes,
          last_updated_at: new Date()
        }
      });
    } catch (error) {
      logger.error('Failed to save deposit/withdraw record:', error.message);
      throw error;
    }
  }

  /**
   * Batch save multiple records
   */
  async batchSaveRecords(records) {
    const results = {
      success: 0,
      failed: 0,
      errors: []
    };

    for (const record of records) {
      try {
        await this.saveDepositWithdrawRecord(record);
        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push({
          record: `${record.exchange}-${record.token}-${record.network}`,
          error: error.message
        });
      }
    }

    logger.info(`Batch save completed: ${results.success} success, ${results.failed} failed`);
    return results;
  }

  /**
   * Query deposit/withdraw records with filters
   */
  async queryRecords(filters = {}) {
    try {
      const where = {};
      const include = {
        exchange: true,
        token: true,
        network: true
      };

      // Apply filters
      if (filters.token) {
        where.token = {
          symbol: filters.token
        };
      }

      if (filters.network && filters.network !== 'ALL') {
        where.network = {
          name: filters.network
        };
      }

      if (filters.exchange && filters.exchange !== 'ALL') {
        where.exchange = {
          name: filters.exchange
        };
      }

      if (filters.can_deposit !== undefined) {
        where.can_deposit = filters.can_deposit;
      }

      if (filters.can_withdraw !== undefined) {
        where.can_withdraw = filters.can_withdraw;
      }

      const records = await this.prisma.depositWithdrawRecord.findMany({
        where,
        include,
        orderBy: [
          { exchange: { name: 'asc' } },
          { token: { symbol: 'asc' } },
          { network: { name: 'asc' } }
        ]
      });

      // Transform records for API response
      return records.map(record => ({
        exchange: record.exchange.name,
        token: record.token.symbol,
        chain: record.network.name,
        can_deposit: record.can_deposit,
        can_withdraw: record.can_withdraw,
        deposit_fee: record.deposit_fee,
        withdrawal_fee: record.withdrawal_fee,
        min_deposit: record.min_deposit,
        min_withdrawal: record.min_withdrawal,
        deposit_confirmations: record.deposit_confirmations,
        withdrawal_confirmations: record.withdrawal_confirmations,
        status_notes: record.status_notes,
        last_updated: record.last_updated_at.toISOString()
      }));
    } catch (error) {
      logger.error('Failed to query records:', error.message);
      throw error;
    }
  }

  /**
   * Get filter options for frontend
   */
  async getFilterOptions() {
    try {
      const [tokens, networks, exchanges] = await Promise.all([
        this.prisma.token.findMany({
          where: { is_active: true },
          orderBy: { symbol: 'asc' }
        }),
        this.prisma.network.findMany({
          where: { is_active: true },
          orderBy: { name: 'asc' }
        }),
        this.prisma.exchange.findMany({
          where: { is_active: true },
          orderBy: { name: 'asc' }
        })
      ]);

      return {
        tokens: tokens.map(t => ({ value: t.symbol, label: t.symbol })),
        chains: [
          { value: 'ALL', label: '所有链 (All Chains)' },
          ...networks.map(n => ({ value: n.name, label: n.display_name }))
        ],
        exchanges: [
          { value: 'ALL', label: '所有交易所 (All Exchanges)' },
          ...exchanges.map(e => ({ value: e.name, label: e.display_name }))
        ]
      };
    } catch (error) {
      logger.error('Failed to get filter options:', error.message);
      throw error;
    }
  }

  /**
   * Get last update timestamp
   */
  async getLastUpdateTimestamp() {
    try {
      const lastRecord = await this.prisma.depositWithdrawRecord.findFirst({
        orderBy: { last_updated_at: 'desc' },
        select: { last_updated_at: true }
      });

      return lastRecord ? lastRecord.last_updated_at.toISOString() : null;
    } catch (error) {
      logger.error('Failed to get last update timestamp:', error.message);
      return null;
    }
  }

  /**
   * Helper methods
   */
  getExchangeApiUrl(exchangeName) {
    const urls = {
      'Binance': 'https://api.binance.com',
      'OKX': 'https://www.okx.com',
      'Bybit': 'https://api.bybit.com',
      'Bitget': 'https://api.bitget.com'
    };
    return urls[exchangeName] || null;
  }

  getTokenName(symbol) {
    const names = {
      'USDT': 'Tether USD',
      'BTC': 'Bitcoin',
      'ETH': 'Ethereum',
      'BNB': 'BNB',
      'USDC': 'USD Coin',
      'XRP': 'Ripple',
      'ADA': 'Cardano',
      'DOGE': 'Dogecoin',
      'SOL': 'Solana',
      'TRX': 'TRON'
    };
    return names[symbol] || symbol;
  }

  getNetworkDisplayName(networkName) {
    const displayNames = {
      'ERC20': 'Ethereum (ERC20)',
      'TRC20': 'TRON (TRC20)',
      'BSC': 'BNB Smart Chain (BSC)',
      'Polygon': 'Polygon (MATIC)',
      'Arbitrum': 'Arbitrum One',
      'Optimism': 'Optimism',
      'Avalanche': 'Avalanche C-Chain',
      'Solana': 'Solana',
      'Bitcoin': 'Bitcoin'
    };
    return displayNames[networkName] || networkName;
  }

  getNetworkChainId(networkName) {
    const chainIds = {
      'ERC20': 1,
      'BSC': 56,
      'Polygon': 137,
      'Arbitrum': 42161,
      'Optimism': 10,
      'Avalanche': 43114
    };
    return chainIds[networkName] || null;
  }
}

// Create singleton instance
const exchangeDatabase = new ExchangeDatabaseService();

module.exports = {
  ExchangeDatabaseService,
  exchangeDatabase
};
