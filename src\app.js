const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

const { errorHandler, notFoundHandler } = require('./middleware/errorHandler');
const monitorManager = require('./services/monitorManager');
const { ExchangeDataManager } = require('./services/exchangeDataManager');
const queueService = require('./services/queue');

// Import routes
const channelRoutes = require('./routes/channels');
const notifyRoutes = require('./routes/notify');
const monitorRoutes = require('./routes/monitors');
const authRoutes = require('./routes/auth');
const { router: exchangeRoutes, setExchangeDataManager } = require('./routes/exchange');

const app = express();

// Security middleware
app.use(helmet());
app.use(cors());

// Logging middleware
app.use(morgan('combined'));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    service: 'DDDDAlert Backend'
  });
});

// API routes
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/channels', channelRoutes);
app.use('/api/v1/notify', notifyRoutes);
app.use('/api/v1/monitors', monitorRoutes);
app.use('/api/v1/exchange', exchangeRoutes);

// Error handling middleware
app.use(notFoundHandler);
app.use(errorHandler);

const PORT = process.env.PORT || 3000;

// Initialize exchange data manager
let exchangeDataManager = null;

if (require.main === module) {
  app.listen(PORT, async () => {
    console.log(`🚀 DDDDAlert Backend Server running on port ${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/health`);

    // Initialize monitoring system
    try {
      await monitorManager.initialize();
      console.log(`🔍 Monitoring system initialized`);
    } catch (error) {
      console.error('⚠️  Monitoring system initialization failed, but server will continue running:', error.message);
      console.log('📝 Note: Monitoring features will be disabled. Please check Redis connection and restart to enable monitoring.');
    }

    // Initialize exchange data management system
    try {
      exchangeDataManager = new ExchangeDataManager(queueService);
      setExchangeDataManager(exchangeDataManager);
      await exchangeDataManager.initialize();
      console.log(`💱 Exchange data management system initialized`);
    } catch (error) {
      console.error('⚠️  Exchange data system initialization failed, but server will continue running:', error.message);
      console.log('📝 Note: Exchange data features will be disabled. Please check Redis connection and restart to enable exchange data collection.');
    }
  });

  // Graceful shutdown
  process.on('SIGTERM', async () => {
    console.log('🛑 SIGTERM received, shutting down gracefully...');
    try {
      if (exchangeDataManager) {
        await exchangeDataManager.shutdown();
        console.log('✅ Exchange data system shut down successfully');
      }
      await monitorManager.shutdown();
      console.log('✅ Monitoring system shut down successfully');
      process.exit(0);
    } catch (error) {
      console.error('❌ Error during shutdown:', error);
      process.exit(1);
    }
  });

  process.on('SIGINT', async () => {
    console.log('🛑 SIGINT received, shutting down gracefully...');
    try {
      if (exchangeDataManager) {
        await exchangeDataManager.shutdown();
        console.log('✅ Exchange data system shut down successfully');
      }
      await monitorManager.shutdown();
      console.log('✅ Monitoring system shut down successfully');
      process.exit(0);
    } catch (error) {
      console.error('❌ Error during shutdown:', error);
      process.exit(1);
    }
  });
}

module.exports = app;
