const request = require('supertest');
const app = require('../src/app');
const { ExchangeDataManager } = require('../src/services/exchangeDataManager');
const { exchangeDatabase } = require('../src/services/exchangeDatabase');

// Mock the exchange data manager
jest.mock('../src/services/exchangeDataManager');
jest.mock('../src/services/exchangeDatabase');

describe('Exchange API Endpoints', () => {
  let mockExchangeDataManager;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create mock exchange data manager
    mockExchangeDataManager = {
      getFilterOptions: jest.fn(),
      queryRecords: jest.fn(),
      getLastUpdateTimestamp: jest.fn(),
      getStatistics: jest.fn(),
      healthCheck: jest.fn(),
      triggerImmediateFetch: jest.fn(),
      triggerExchangeFetch: jest.fn(),
      getStatus: jest.fn()
    };

    // Mock the constructor
    ExchangeDataManager.mockImplementation(() => mockExchangeDataManager);
  });

  describe('GET /api/v1/exchange/options', () => {
    it('should return filter options successfully', async () => {
      const mockOptions = {
        tokens: [
          { value: 'USDT', label: 'USDT' },
          { value: 'BTC', label: 'BTC' }
        ],
        chains: [
          { value: 'ALL', label: '所有链 (All Chains)' },
          { value: 'ERC20', label: 'Ethereum (ERC20)' }
        ],
        exchanges: [
          { value: 'ALL', label: '所有交易所 (All Exchanges)' },
          { value: 'Binance', label: 'Binance' }
        ]
      };

      mockExchangeDataManager.getFilterOptions.mockResolvedValue(mockOptions);

      const response = await request(app)
        .get('/api/v1/exchange/options')
        .expect(200);

      expect(response.body).toEqual(mockOptions);
      expect(mockExchangeDataManager.getFilterOptions).toHaveBeenCalledTimes(1);
    });

    it('should handle service unavailable', async () => {
      // Test when exchange data manager is not initialized
      const response = await request(app)
        .get('/api/v1/exchange/options')
        .expect(503);

      expect(response.body).toHaveProperty('error', 'Exchange data service unavailable');
    });
  });

  describe('GET /api/v1/exchange/status', () => {
    it('should return exchange status with valid token', async () => {
      const mockRecords = [
        {
          exchange: 'Binance',
          token: 'USDT',
          chain: 'ERC20',
          can_deposit: true,
          can_withdraw: true,
          deposit_fee: '0',
          withdrawal_fee: '1.0',
          min_deposit: '10.0',
          min_withdrawal: '20.0',
          deposit_confirmations: 12,
          withdrawal_confirmations: 12,
          status_notes: null
        }
      ];

      const mockTimestamp = '2025-01-27T10:30:00Z';

      mockExchangeDataManager.queryRecords.mockResolvedValue(mockRecords);
      mockExchangeDataManager.getLastUpdateTimestamp.mockResolvedValue(mockTimestamp);

      const response = await request(app)
        .get('/api/v1/exchange/status?token=USDT')
        .expect(200);

      expect(response.body).toEqual({
        data: mockRecords,
        last_updated_timestamp: mockTimestamp,
        total_records: mockRecords.length
      });

      expect(mockExchangeDataManager.queryRecords).toHaveBeenCalledWith({
        token: 'USDT'
      });
    });

    it('should return 400 for missing token parameter', async () => {
      const response = await request(app)
        .get('/api/v1/exchange/status')
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Validation error');
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: 'token',
            message: expect.stringContaining('required')
          })
        ])
      );
    });

    it('should return 400 for invalid token format', async () => {
      const response = await request(app)
        .get('/api/v1/exchange/status?token=invalid-token!')
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Validation error');
    });

    it('should handle optional chain and exchange parameters', async () => {
      const mockRecords = [];
      mockExchangeDataManager.queryRecords.mockResolvedValue(mockRecords);
      mockExchangeDataManager.getLastUpdateTimestamp.mockResolvedValue(null);

      const response = await request(app)
        .get('/api/v1/exchange/status?token=BTC&chain=ERC20&exchange=Binance')
        .expect(200);

      expect(mockExchangeDataManager.queryRecords).toHaveBeenCalledWith({
        token: 'BTC',
        network: 'ERC20',
        exchange: 'Binance'
      });
    });

    it('should ignore ALL values for chain and exchange', async () => {
      const mockRecords = [];
      mockExchangeDataManager.queryRecords.mockResolvedValue(mockRecords);
      mockExchangeDataManager.getLastUpdateTimestamp.mockResolvedValue(null);

      const response = await request(app)
        .get('/api/v1/exchange/status?token=ETH&chain=ALL&exchange=ALL')
        .expect(200);

      expect(mockExchangeDataManager.queryRecords).toHaveBeenCalledWith({
        token: 'ETH'
      });
    });
  });

  describe('GET /api/v1/exchange/statistics', () => {
    it('should return system statistics', async () => {
      const mockStats = {
        totalRecords: 1000,
        activeExchanges: 4,
        activeTokens: 50,
        activeNetworks: 9,
        lastUpdate: '2025-01-27T10:30:00Z',
        systemStatus: {
          isInitialized: true,
          worker: { isRunning: true }
        }
      };

      mockExchangeDataManager.getStatistics.mockResolvedValue(mockStats);

      const response = await request(app)
        .get('/api/v1/exchange/statistics')
        .expect(200);

      expect(response.body).toEqual(mockStats);
    });
  });

  describe('GET /api/v1/exchange/health', () => {
    it('should return healthy status', async () => {
      const mockHealth = {
        healthy: true,
        status: { isInitialized: true },
        lastUpdate: '2025-01-27T10:30:00Z',
        dataStale: false,
        timestamp: expect.any(String)
      };

      mockExchangeDataManager.healthCheck.mockResolvedValue(mockHealth);

      const response = await request(app)
        .get('/api/v1/exchange/health')
        .expect(200);

      expect(response.body.healthy).toBe(true);
    });

    it('should return unhealthy status', async () => {
      const mockHealth = {
        healthy: false,
        status: { isInitialized: false },
        lastUpdate: null,
        dataStale: true,
        timestamp: expect.any(String)
      };

      mockExchangeDataManager.healthCheck.mockResolvedValue(mockHealth);

      const response = await request(app)
        .get('/api/v1/exchange/health')
        .expect(503);

      expect(response.body.healthy).toBe(false);
    });
  });

  describe('POST /api/v1/exchange/refresh', () => {
    it('should trigger immediate refresh successfully', async () => {
      const mockResult = {
        success: true,
        jobId: 'job-123',
        message: 'Immediate fetch job scheduled successfully'
      };

      mockExchangeDataManager.triggerImmediateFetch.mockResolvedValue(mockResult);

      const response = await request(app)
        .post('/api/v1/exchange/refresh')
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        message: 'Data refresh triggered successfully',
        job_id: 'job-123'
      });
    });
  });

  describe('POST /api/v1/exchange/refresh/:exchangeName', () => {
    it('should trigger exchange-specific refresh successfully', async () => {
      const mockResult = {
        success: true,
        jobId: 'job-456',
        exchange: 'Binance'
      };

      mockExchangeDataManager.triggerExchangeFetch.mockResolvedValue(mockResult);

      const response = await request(app)
        .post('/api/v1/exchange/refresh/Binance')
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        message: 'Data refresh for Binance triggered successfully',
        exchange: 'Binance',
        job_id: 'job-456'
      });
    });

    it('should return 400 for invalid exchange name', async () => {
      const response = await request(app)
        .post('/api/v1/exchange/refresh/InvalidExchange')
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Invalid exchange name');
    });
  });

  describe('GET /api/v1/exchange/system/status', () => {
    it('should return system status', async () => {
      const mockStatus = {
        isInitialized: true,
        worker: { isRunning: true },
        scheduledJobs: []
      };

      mockExchangeDataManager.getStatus.mockReturnValue(mockStatus);

      const response = await request(app)
        .get('/api/v1/exchange/system/status')
        .expect(200);

      expect(response.body).toHaveProperty('system_status', mockStatus);
      expect(response.body).toHaveProperty('timestamp');
    });
  });
});

describe('Exchange Validation', () => {
  describe('Token validation', () => {
    it('should accept valid token symbols', async () => {
      const validTokens = ['BTC', 'ETH', 'USDT', 'BNB123'];
      
      for (const token of validTokens) {
        const response = await request(app)
          .get(`/api/v1/exchange/status?token=${token}`)
          .expect(200);
      }
    });

    it('should reject invalid token symbols', async () => {
      const invalidTokens = ['', 'btc', 'BTC!', 'very-long-token-symbol-name'];
      
      for (const token of invalidTokens) {
        const response = await request(app)
          .get(`/api/v1/exchange/status?token=${token}`)
          .expect(400);
      }
    });
  });

  describe('Chain validation', () => {
    it('should accept valid chain names', async () => {
      const validChains = ['ALL', 'ERC20', 'TRC20', 'BSC', 'Polygon'];
      
      for (const chain of validChains) {
        const response = await request(app)
          .get(`/api/v1/exchange/status?token=USDT&chain=${chain}`)
          .expect(200);
      }
    });

    it('should reject invalid chain names', async () => {
      const response = await request(app)
        .get('/api/v1/exchange/status?token=USDT&chain=InvalidChain')
        .expect(400);
    });
  });

  describe('Exchange validation', () => {
    it('should accept valid exchange names', async () => {
      const validExchanges = ['ALL', 'Binance', 'OKX', 'Bybit', 'Bitget'];
      
      for (const exchange of validExchanges) {
        const response = await request(app)
          .get(`/api/v1/exchange/status?token=USDT&exchange=${exchange}`)
          .expect(200);
      }
    });

    it('should reject invalid exchange names', async () => {
      const response = await request(app)
        .get('/api/v1/exchange/status?token=USDT&exchange=InvalidExchange')
        .expect(400);
    });
  });
});
