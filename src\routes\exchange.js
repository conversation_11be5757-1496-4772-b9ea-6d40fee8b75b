const express = require('express');
const { ExchangeDataManager } = require('../services/exchangeDataManager');
const logger = require('../utils/logger');
const { validateExchangeQuery } = require('../validators/exchange');

const router = express.Router();

// Initialize exchange data manager (will be set by app.js)
let exchangeDataManager = null;

/**
 * Set exchange data manager instance
 */
function setExchangeDataManager(manager) {
  exchangeDataManager = manager;
}

/**
 * Middleware to check if exchange data manager is available
 */
function requireExchangeManager(req, res, next) {
  if (!exchangeDataManager) {
    return res.status(503).json({
      error: 'Exchange data service unavailable',
      message: 'Exchange data manager not initialized'
    });
  }
  next();
}

/**
 * GET /api/v1/exchange/options
 * Get filter options for frontend
 */
router.get('/options', requireExchangeManager, async (req, res) => {
  try {
    logger.info('Fetching exchange filter options');
    
    const options = await exchangeDataManager.getFilterOptions();
    
    res.json(options);
  } catch (error) {
    logger.error('Failed to get filter options:', error.message);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch filter options'
    });
  }
});

/**
 * GET /api/v1/exchange/status
 * Query deposit/withdraw status with filters
 */
router.get('/status', requireExchangeManager, validateExchangeQuery, async (req, res) => {
  try {
    const { token, chain, exchange } = req.query;
    
    logger.info('Querying exchange status:', { token, chain, exchange });
    
    // Build filters
    const filters = {};
    
    if (token) {
      filters.token = token;
    }
    
    if (chain && chain !== 'ALL') {
      filters.network = chain;
    }
    
    if (exchange && exchange !== 'ALL') {
      filters.exchange = exchange;
    }
    
    // Query records
    const records = await exchangeDataManager.queryRecords(filters);
    
    // Get last update timestamp
    const lastUpdated = await exchangeDataManager.getLastUpdateTimestamp();
    
    res.json({
      data: records,
      last_updated_timestamp: lastUpdated,
      total_records: records.length
    });
  } catch (error) {
    logger.error('Failed to query exchange status:', error.message);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to query exchange status'
    });
  }
});

/**
 * GET /api/v1/exchange/statistics
 * Get system statistics
 */
router.get('/statistics', requireExchangeManager, async (req, res) => {
  try {
    logger.info('Fetching exchange statistics');
    
    const stats = await exchangeDataManager.getStatistics();
    
    res.json(stats);
  } catch (error) {
    logger.error('Failed to get statistics:', error.message);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch statistics'
    });
  }
});

/**
 * GET /api/v1/exchange/health
 * Health check endpoint
 */
router.get('/health', requireExchangeManager, async (req, res) => {
  try {
    const health = await exchangeDataManager.healthCheck();
    
    const statusCode = health.healthy ? 200 : 503;
    res.status(statusCode).json(health);
  } catch (error) {
    logger.error('Health check failed:', error.message);
    res.status(503).json({
      healthy: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * POST /api/v1/exchange/refresh
 * Trigger immediate data refresh for all exchanges
 */
router.post('/refresh', requireExchangeManager, async (req, res) => {
  try {
    logger.info('Triggering immediate exchange data refresh');
    
    const result = await exchangeDataManager.triggerImmediateFetch();
    
    res.json({
      success: true,
      message: 'Data refresh triggered successfully',
      job_id: result.jobId
    });
  } catch (error) {
    logger.error('Failed to trigger data refresh:', error.message);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to trigger data refresh'
    });
  }
});

/**
 * POST /api/v1/exchange/refresh/:exchangeName
 * Trigger immediate data refresh for specific exchange
 */
router.post('/refresh/:exchangeName', requireExchangeManager, async (req, res) => {
  try {
    const { exchangeName } = req.params;
    
    // Validate exchange name
    const validExchanges = ['Binance', 'OKX', 'Bybit', 'Bitget'];
    if (!validExchanges.includes(exchangeName)) {
      return res.status(400).json({
        error: 'Invalid exchange name',
        message: `Exchange must be one of: ${validExchanges.join(', ')}`
      });
    }
    
    logger.info(`Triggering immediate data refresh for ${exchangeName}`);
    
    const result = await exchangeDataManager.triggerExchangeFetch(exchangeName);
    
    res.json({
      success: true,
      message: `Data refresh for ${exchangeName} triggered successfully`,
      exchange: exchangeName,
      job_id: result.jobId
    });
  } catch (error) {
    logger.error(`Failed to trigger data refresh for ${req.params.exchangeName}:`, error.message);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to trigger data refresh'
    });
  }
});

/**
 * GET /api/v1/exchange/system/status
 * Get system status (for admin/debugging)
 */
router.get('/system/status', requireExchangeManager, async (req, res) => {
  try {
    const status = exchangeDataManager.getStatus();
    
    res.json({
      system_status: status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to get system status:', error.message);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to get system status'
    });
  }
});

module.exports = {
  router,
  setExchangeDataManager
};
