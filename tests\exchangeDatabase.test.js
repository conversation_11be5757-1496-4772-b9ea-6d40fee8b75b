const { ExchangeDatabaseService } = require('../src/services/exchangeDatabase');

// Mock Prisma Client
const mockPrisma = {
  exchange: {
    upsert: jest.fn(),
    findMany: jest.fn()
  },
  token: {
    upsert: jest.fn(),
    findMany: jest.fn()
  },
  network: {
    upsert: jest.fn(),
    findMany: jest.fn()
  },
  depositWithdrawRecord: {
    upsert: jest.fn(),
    findMany: jest.fn(),
    findFirst: jest.fn(),
    count: jest.fn()
  }
};

// Mock the PrismaClient constructor
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn(() => mockPrisma)
}));

describe('ExchangeDatabaseService', () => {
  let databaseService;

  beforeEach(() => {
    jest.clearAllMocks();
    databaseService = new ExchangeDatabaseService();
  });

  describe('getOrCreateExchange', () => {
    it('should create or update exchange record', async () => {
      const mockExchange = {
        id: 'exchange-1',
        name: 'Binance',
        display_name: 'Binance',
        api_base_url: 'https://api.binance.com'
      };

      mockPrisma.exchange.upsert.mockResolvedValue(mockExchange);

      const result = await databaseService.getOrCreateExchange({ exchange: 'Binance' });

      expect(result).toEqual(mockExchange);
      expect(mockPrisma.exchange.upsert).toHaveBeenCalledWith({
        where: { name: 'Binance' },
        update: {
          display_name: 'Binance',
          updated_at: expect.any(Date)
        },
        create: {
          name: 'Binance',
          display_name: 'Binance',
          api_base_url: 'https://api.binance.com'
        }
      });
    });
  });

  describe('getOrCreateToken', () => {
    it('should create or update token record', async () => {
      const mockToken = {
        id: 'token-1',
        symbol: 'USDT',
        name: 'Tether USD'
      };

      mockPrisma.token.upsert.mockResolvedValue(mockToken);

      const result = await databaseService.getOrCreateToken('USDT');

      expect(result).toEqual(mockToken);
      expect(mockPrisma.token.upsert).toHaveBeenCalledWith({
        where: { symbol: 'USDT' },
        update: {
          updated_at: expect.any(Date)
        },
        create: {
          symbol: 'USDT',
          name: 'Tether USD'
        }
      });
    });
  });

  describe('getOrCreateNetwork', () => {
    it('should create or update network record', async () => {
      const mockNetwork = {
        id: 'network-1',
        name: 'ERC20',
        display_name: 'Ethereum (ERC20)',
        chain_id: 1
      };

      mockPrisma.network.upsert.mockResolvedValue(mockNetwork);

      const result = await databaseService.getOrCreateNetwork('ERC20');

      expect(result).toEqual(mockNetwork);
      expect(mockPrisma.network.upsert).toHaveBeenCalledWith({
        where: { name: 'ERC20' },
        update: {
          updated_at: expect.any(Date)
        },
        create: {
          name: 'ERC20',
          display_name: 'Ethereum (ERC20)',
          chain_id: 1
        }
      });
    });
  });

  describe('saveDepositWithdrawRecord', () => {
    it('should save deposit/withdraw record successfully', async () => {
      const recordData = {
        exchange: 'Binance',
        token: 'USDT',
        network: 'ERC20',
        can_deposit: true,
        can_withdraw: true,
        deposit_fee: '0',
        withdrawal_fee: '1.0',
        min_deposit: '10.0',
        min_withdrawal: '20.0',
        deposit_confirmations: 12,
        withdrawal_confirmations: 12,
        status_notes: null
      };

      const mockExchange = { id: 'exchange-1' };
      const mockToken = { id: 'token-1' };
      const mockNetwork = { id: 'network-1' };
      const mockRecord = { id: 'record-1', ...recordData };

      mockPrisma.exchange.upsert.mockResolvedValue(mockExchange);
      mockPrisma.token.upsert.mockResolvedValue(mockToken);
      mockPrisma.network.upsert.mockResolvedValue(mockNetwork);
      mockPrisma.depositWithdrawRecord.upsert.mockResolvedValue(mockRecord);

      const result = await databaseService.saveDepositWithdrawRecord(recordData);

      expect(result).toEqual(mockRecord);
      expect(mockPrisma.depositWithdrawRecord.upsert).toHaveBeenCalledWith({
        where: {
          exchange_id_token_id_network_id: {
            exchange_id: 'exchange-1',
            token_id: 'token-1',
            network_id: 'network-1'
          }
        },
        update: expect.objectContaining({
          can_deposit: true,
          can_withdraw: true,
          last_updated_at: expect.any(Date)
        }),
        create: expect.objectContaining({
          exchange_id: 'exchange-1',
          token_id: 'token-1',
          network_id: 'network-1',
          can_deposit: true,
          can_withdraw: true
        })
      });
    });
  });

  describe('batchSaveRecords', () => {
    it('should save multiple records and return summary', async () => {
      const records = [
        {
          exchange: 'Binance',
          token: 'USDT',
          network: 'ERC20',
          can_deposit: true,
          can_withdraw: true
        },
        {
          exchange: 'OKX',
          token: 'BTC',
          network: 'Bitcoin',
          can_deposit: true,
          can_withdraw: false
        }
      ];

      // Mock successful saves
      jest.spyOn(databaseService, 'saveDepositWithdrawRecord')
        .mockResolvedValueOnce({ id: 'record-1' })
        .mockResolvedValueOnce({ id: 'record-2' });

      const result = await databaseService.batchSaveRecords(records);

      expect(result).toEqual({
        success: 2,
        failed: 0,
        errors: []
      });
    });

    it('should handle partial failures', async () => {
      const records = [
        { exchange: 'Binance', token: 'USDT', network: 'ERC20' },
        { exchange: 'OKX', token: 'BTC', network: 'Bitcoin' }
      ];

      jest.spyOn(databaseService, 'saveDepositWithdrawRecord')
        .mockResolvedValueOnce({ id: 'record-1' })
        .mockRejectedValueOnce(new Error('Database error'));

      const result = await databaseService.batchSaveRecords(records);

      expect(result).toEqual({
        success: 1,
        failed: 1,
        errors: [{
          record: 'OKX-BTC-Bitcoin',
          error: 'Database error'
        }]
      });
    });
  });

  describe('queryRecords', () => {
    it('should query records with filters', async () => {
      const mockRecords = [
        {
          exchange: { name: 'Binance' },
          token: { symbol: 'USDT' },
          network: { name: 'ERC20' },
          can_deposit: true,
          can_withdraw: true,
          deposit_fee: '0',
          withdrawal_fee: '1.0',
          last_updated_at: new Date()
        }
      ];

      mockPrisma.depositWithdrawRecord.findMany.mockResolvedValue(mockRecords);

      const filters = {
        token: 'USDT',
        network: 'ERC20',
        exchange: 'Binance'
      };

      const result = await databaseService.queryRecords(filters);

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        exchange: 'Binance',
        token: 'USDT',
        chain: 'ERC20',
        can_deposit: true,
        can_withdraw: true
      });

      expect(mockPrisma.depositWithdrawRecord.findMany).toHaveBeenCalledWith({
        where: {
          token: { symbol: 'USDT' },
          network: { name: 'ERC20' },
          exchange: { name: 'Binance' }
        },
        include: {
          exchange: true,
          token: true,
          network: true
        },
        orderBy: [
          { exchange: { name: 'asc' } },
          { token: { symbol: 'asc' } },
          { network: { name: 'asc' } }
        ]
      });
    });

    it('should handle ALL filter values', async () => {
      mockPrisma.depositWithdrawRecord.findMany.mockResolvedValue([]);

      const filters = {
        token: 'USDT',
        network: 'ALL',
        exchange: 'ALL'
      };

      await databaseService.queryRecords(filters);

      expect(mockPrisma.depositWithdrawRecord.findMany).toHaveBeenCalledWith({
        where: {
          token: { symbol: 'USDT' }
        },
        include: expect.any(Object),
        orderBy: expect.any(Array)
      });
    });
  });

  describe('getFilterOptions', () => {
    it('should return filter options', async () => {
      const mockTokens = [
        { symbol: 'USDT' },
        { symbol: 'BTC' }
      ];
      const mockNetworks = [
        { name: 'ERC20', display_name: 'Ethereum (ERC20)' },
        { name: 'TRC20', display_name: 'TRON (TRC20)' }
      ];
      const mockExchanges = [
        { name: 'Binance', display_name: 'Binance' },
        { name: 'OKX', display_name: 'OKX' }
      ];

      mockPrisma.token.findMany.mockResolvedValue(mockTokens);
      mockPrisma.network.findMany.mockResolvedValue(mockNetworks);
      mockPrisma.exchange.findMany.mockResolvedValue(mockExchanges);

      const result = await databaseService.getFilterOptions();

      expect(result).toEqual({
        tokens: [
          { value: 'USDT', label: 'USDT' },
          { value: 'BTC', label: 'BTC' }
        ],
        chains: [
          { value: 'ALL', label: '所有链 (All Chains)' },
          { value: 'ERC20', label: 'Ethereum (ERC20)' },
          { value: 'TRC20', label: 'TRON (TRC20)' }
        ],
        exchanges: [
          { value: 'ALL', label: '所有交易所 (All Exchanges)' },
          { value: 'Binance', label: 'Binance' },
          { value: 'OKX', label: 'OKX' }
        ]
      });
    });
  });

  describe('getLastUpdateTimestamp', () => {
    it('should return last update timestamp', async () => {
      const mockDate = new Date('2025-01-27T10:30:00Z');
      mockPrisma.depositWithdrawRecord.findFirst.mockResolvedValue({
        last_updated_at: mockDate
      });

      const result = await databaseService.getLastUpdateTimestamp();

      expect(result).toBe(mockDate.toISOString());
    });

    it('should return null when no records exist', async () => {
      mockPrisma.depositWithdrawRecord.findFirst.mockResolvedValue(null);

      const result = await databaseService.getLastUpdateTimestamp();

      expect(result).toBeNull();
    });
  });

  describe('Helper methods', () => {
    it('should get correct exchange API URL', () => {
      expect(databaseService.getExchangeApiUrl('Binance')).toBe('https://api.binance.com');
      expect(databaseService.getExchangeApiUrl('OKX')).toBe('https://www.okx.com');
      expect(databaseService.getExchangeApiUrl('Unknown')).toBeNull();
    });

    it('should get correct token name', () => {
      expect(databaseService.getTokenName('USDT')).toBe('Tether USD');
      expect(databaseService.getTokenName('BTC')).toBe('Bitcoin');
      expect(databaseService.getTokenName('UNKNOWN')).toBe('UNKNOWN');
    });

    it('should get correct network display name', () => {
      expect(databaseService.getNetworkDisplayName('ERC20')).toBe('Ethereum (ERC20)');
      expect(databaseService.getNetworkDisplayName('TRC20')).toBe('TRON (TRC20)');
      expect(databaseService.getNetworkDisplayName('UNKNOWN')).toBe('UNKNOWN');
    });

    it('should get correct network chain ID', () => {
      expect(databaseService.getNetworkChainId('ERC20')).toBe(1);
      expect(databaseService.getNetworkChainId('BSC')).toBe(56);
      expect(databaseService.getNetworkChainId('UNKNOWN')).toBeNull();
    });
  });
});
