// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User table
model User {
  id           String   @id @default(cuid())
  username     String   @unique @db.VarChar(50) // 支持以太坊地址长度 (42字符)
  password_hash String?
  api_key      String   @unique @default(cuid())
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt

  // Relations
  notification_channels NotificationChannel[]
  monitoring_rules     MonitoringRule[]

  @@map("users")
}

// Notification channels table
model NotificationChannel {
  id           String                @id @default(cuid())
  user_id      String
  channel_name String
  channel_type NotificationChannelType
  credentials  Json                  // Encrypted credentials
  is_enabled   Boolean               @default(true)
  created_at   DateTime              @default(now())
  updated_at   DateTime              @updatedAt

  // Relations
  user             User             @relation(fields: [user_id], references: [id], onDelete: Cascade)
  monitoring_rules MonitoringRule[]

  @@map("notification_channels")
}

// Monitoring rules table
model MonitoringRule {
  id                         String                    @id @default(cuid())
  user_id                    String
  rule_name                  String
  rule_type                  MonitoringRuleType
  chain_id                   Int
  config                     Json                      // Rule-specific configuration
  notification_channel_id    String
  notification_interval_seconds Int                   @default(300)
  stop_after_triggered       Boolean                   @default(false)
  is_active                  Boolean                   @default(true)
  last_triggered_at          DateTime?
  created_at                 DateTime                  @default(now())
  updated_at                 DateTime                  @updatedAt

  // Relations
  user                 User                @relation(fields: [user_id], references: [id], onDelete: Cascade)
  notification_channel NotificationChannel @relation(fields: [notification_channel_id], references: [id], onDelete: Cascade)

  @@map("monitoring_rules")
}

// Enums
enum NotificationChannelType {
  TELEGRAM
  DISCORD
  DINGTALK
  FEISHU
  WEBHOOK
  BARK
  EMAIL

  @@map("notification_channel_type")
}

enum MonitoringRuleType {
  WALLET_TRANSFER
  GAS_PRICE
  DEX_SWAP
  CONTRACT_EVENT

  @@map("monitoring_rule_type")
}

// Exchange information table
model Exchange {
  id           String   @id @default(cuid())
  name         String   @unique @db.VarChar(50) // e.g., "Binance", "OKX", "Bybit", "Bitget"
  display_name String   @db.VarChar(100) // e.g., "Binance", "OKX", "Bybit", "Bitget"
  api_base_url String?  @db.VarChar(255) // API base URL
  is_active    Boolean  @default(true)
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt

  // Relations
  deposit_withdraw_records DepositWithdrawRecord[]

  @@map("exchanges")
}

// Token information table
model Token {
  id           String   @id @default(cuid())
  symbol       String   @unique @db.VarChar(20) // e.g., "USDT", "BTC", "ETH"
  name         String   @db.VarChar(100) // e.g., "Tether USD", "Bitcoin", "Ethereum"
  is_active    Boolean  @default(true)
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt

  // Relations
  deposit_withdraw_records DepositWithdrawRecord[]

  @@map("tokens")
}

// Network/Chain information table
model Network {
  id           String   @id @default(cuid())
  name         String   @unique @db.VarChar(50) // e.g., "ERC20", "TRC20", "BSC", "Polygon"
  display_name String   @db.VarChar(100) // e.g., "Ethereum (ERC20)", "TRON (TRC20)"
  chain_id     Int?     // Blockchain chain ID if applicable
  is_active    Boolean  @default(true)
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt

  // Relations
  deposit_withdraw_records DepositWithdrawRecord[]

  @@map("networks")
}

// Deposit and withdraw status records table
model DepositWithdrawRecord {
  id                        String   @id @default(cuid())
  exchange_id               String
  token_id                  String
  network_id                String

  // Deposit information
  can_deposit               Boolean  @default(false)
  deposit_fee               String?  @db.VarChar(50) // Store as string to handle decimal precision
  min_deposit               String?  @db.VarChar(50)
  deposit_confirmations     Int?

  // Withdraw information
  can_withdraw              Boolean  @default(false)
  withdrawal_fee            String?  @db.VarChar(50)
  min_withdrawal            String?  @db.VarChar(50)
  withdrawal_confirmations  Int?

  // Status and notes
  status_notes              String?  @db.Text
  last_updated_at           DateTime @default(now())
  created_at                DateTime @default(now())

  // Relations
  exchange Exchange @relation(fields: [exchange_id], references: [id], onDelete: Cascade)
  token    Token    @relation(fields: [token_id], references: [id], onDelete: Cascade)
  network  Network  @relation(fields: [network_id], references: [id], onDelete: Cascade)

  // Unique constraint to prevent duplicate records
  @@unique([exchange_id, token_id, network_id])
  @@map("deposit_withdraw_records")
}
