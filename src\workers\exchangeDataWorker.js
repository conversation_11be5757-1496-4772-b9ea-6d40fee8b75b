const { Worker } = require('bullmq');
const { exchangeManager } = require('../services/exchangeApi');
const { exchangeDatabase } = require('../services/exchangeDatabase');
const logger = require('../utils/logger');

/**
 * Exchange Data Worker
 * Processes jobs to fetch and update exchange deposit/withdraw data
 */
class ExchangeDataWorker {
  constructor(queueService) {
    this.queueService = queueService;
    this.worker = null;
    this.isRunning = false;
  }

  /**
   * Initialize the worker
   */
  async initialize() {
    try {
      if (this.worker) {
        logger.warn('Exchange data worker already initialized');
        return;
      }

      this.worker = new Worker(
        'exchange-data-queue',
        this.processJob.bind(this),
        {
          connection: this.queueService.getRedisConnection(),
          concurrency: 1, // Process one job at a time to avoid API rate limits
          removeOnComplete: 10, // Keep last 10 completed jobs
          removeOnFail: 50, // Keep last 50 failed jobs
        }
      );

      this.worker.on('completed', (job) => {
        logger.info(`Exchange data job ${job.id} completed successfully`);
      });

      this.worker.on('failed', (job, err) => {
        logger.error(`Exchange data job ${job.id} failed:`, err.message);
      });

      this.worker.on('error', (err) => {
        logger.error('Exchange data worker error:', err.message);
      });

      this.isRunning = true;
      logger.info('Exchange data worker initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize exchange data worker:', error.message);
      throw error;
    }
  }

  /**
   * Process exchange data job
   */
  async processJob(job) {
    const { type, exchangeName } = job.data;
    
    try {
      logger.info(`Processing exchange data job: ${type}`, { exchangeName });

      switch (type) {
        case 'fetch-all-exchanges':
          return await this.fetchAllExchangesData();
        
        case 'fetch-single-exchange':
          return await this.fetchSingleExchangeData(exchangeName);
        
        default:
          throw new Error(`Unknown job type: ${type}`);
      }
    } catch (error) {
      logger.error(`Exchange data job processing failed:`, error.message);
      throw error;
    }
  }

  /**
   * Fetch data from all exchanges
   */
  async fetchAllExchangesData() {
    const startTime = Date.now();
    logger.info('Starting to fetch data from all exchanges...');

    try {
      // Fetch data from all exchanges
      const result = await exchangeManager.fetchAllExchangeData();
      
      if (result.records.length === 0) {
        logger.warn('No records fetched from any exchange');
        return {
          success: false,
          message: 'No data fetched',
          errors: result.errors
        };
      }

      // Save to database
      const saveResult = await exchangeDatabase.batchSaveRecords(result.records);
      
      const duration = Date.now() - startTime;
      const summary = {
        success: true,
        duration: `${duration}ms`,
        totalRecords: result.records.length,
        savedRecords: saveResult.success,
        failedRecords: saveResult.failed,
        exchangeErrors: result.errors,
        saveErrors: saveResult.errors,
        timestamp: result.timestamp
      };

      logger.info('All exchanges data fetch completed:', summary);
      return summary;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(`All exchanges data fetch failed after ${duration}ms:`, error.message);
      throw error;
    }
  }

  /**
   * Fetch data from single exchange
   */
  async fetchSingleExchangeData(exchangeName) {
    const startTime = Date.now();
    logger.info(`Starting to fetch data from ${exchangeName}...`);

    try {
      // Fetch data from specific exchange
      const result = await exchangeManager.fetchExchangeData(exchangeName);
      
      if (result.records.length === 0) {
        logger.warn(`No records fetched from ${exchangeName}`);
        return {
          success: false,
          exchange: exchangeName,
          message: 'No data fetched',
          errors: result.errors
        };
      }

      // Save to database
      const saveResult = await exchangeDatabase.batchSaveRecords(result.records);
      
      const duration = Date.now() - startTime;
      const summary = {
        success: true,
        exchange: exchangeName,
        duration: `${duration}ms`,
        totalRecords: result.records.length,
        savedRecords: saveResult.success,
        failedRecords: saveResult.failed,
        exchangeErrors: result.errors,
        saveErrors: saveResult.errors,
        timestamp: result.timestamp
      };

      logger.info(`${exchangeName} data fetch completed:`, summary);
      return summary;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(`${exchangeName} data fetch failed after ${duration}ms:`, error.message);
      throw error;
    }
  }

  /**
   * Shutdown the worker
   */
  async shutdown() {
    if (this.worker && this.isRunning) {
      logger.info('Shutting down exchange data worker...');
      await this.worker.close();
      this.isRunning = false;
      logger.info('Exchange data worker shut down successfully');
    }
  }

  /**
   * Get worker status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      workerExists: !!this.worker
    };
  }
}

/**
 * Exchange Data Scheduler
 * Manages scheduling of exchange data fetch jobs
 */
class ExchangeDataScheduler {
  constructor(queueService) {
    this.queueService = queueService;
    this.scheduledJobs = new Map();
  }

  /**
   * Schedule regular data fetching
   */
  async scheduleRegularFetch() {
    try {
      // Schedule job to run every 15 minutes
      const jobOptions = {
        repeat: {
          pattern: '*/15 * * * *', // Every 15 minutes
        },
        removeOnComplete: 5,
        removeOnFail: 10,
      };

      const job = await this.queueService.addJob(
        'exchange-data-queue',
        'scheduled-fetch-all',
        {
          type: 'fetch-all-exchanges',
          scheduledAt: new Date().toISOString()
        },
        jobOptions
      );

      this.scheduledJobs.set('regular-fetch', job);
      logger.info('Regular exchange data fetch scheduled (every 15 minutes)');
      
      return job;
    } catch (error) {
      logger.error('Failed to schedule regular fetch:', error.message);
      throw error;
    }
  }

  /**
   * Schedule immediate data fetch
   */
  async scheduleImmediateFetch(exchangeName = null) {
    try {
      const jobData = exchangeName 
        ? {
            type: 'fetch-single-exchange',
            exchangeName: exchangeName,
            triggeredAt: new Date().toISOString()
          }
        : {
            type: 'fetch-all-exchanges',
            triggeredAt: new Date().toISOString()
          };

      const job = await this.queueService.addJob(
        'exchange-data-queue',
        'immediate-fetch',
        jobData,
        {
          priority: 10, // High priority for immediate jobs
          removeOnComplete: 5,
          removeOnFail: 10,
        }
      );

      logger.info(`Immediate exchange data fetch scheduled${exchangeName ? ` for ${exchangeName}` : ' for all exchanges'}`);
      return job;
    } catch (error) {
      logger.error('Failed to schedule immediate fetch:', error.message);
      throw error;
    }
  }

  /**
   * Cancel scheduled jobs
   */
  async cancelScheduledJobs() {
    try {
      for (const [name, job] of this.scheduledJobs) {
        try {
          await job.remove();
          logger.info(`Cancelled scheduled job: ${name}`);
        } catch (error) {
          logger.warn(`Failed to cancel job ${name}:`, error.message);
        }
      }
      
      this.scheduledJobs.clear();
      logger.info('All scheduled exchange data jobs cancelled');
    } catch (error) {
      logger.error('Failed to cancel scheduled jobs:', error.message);
      throw error;
    }
  }

  /**
   * Get scheduled jobs status
   */
  getScheduledJobs() {
    const jobs = [];
    for (const [name, job] of this.scheduledJobs) {
      jobs.push({
        name: name,
        id: job.id,
        opts: job.opts
      });
    }
    return jobs;
  }
}

module.exports = {
  ExchangeDataWorker,
  ExchangeDataScheduler
};
