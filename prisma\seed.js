const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create a demo user
  const hashedPassword = await bcrypt.hash('demo123456', 10);
  
  const demoUser = await prisma.user.upsert({
    where: { username: 'demo' },
    update: {},
    create: {
      username: 'demo',
      password_hash: hashedPassword,
      api_key: uuidv4()
    }
  });

  console.log('✅ Created demo user:', {
    id: demoUser.id,
    username: demoUser.username,
    api_key: demoUser.api_key
  });

  // Create sample notification channels
  const telegramChannel = await prisma.notificationChannel.upsert({
    where: { id: 'demo-telegram-channel' },
    update: {},
    create: {
      id: 'demo-telegram-channel',
      user_id: demoUser.id,
      channel_name: 'Demo Telegram Bot',
      channel_type: 'TELEGRAM',
      credentials: {
        bot_token: 'your-telegram-bot-token',
        chat_id: 'your-chat-id'
      },
      is_enabled: true
    }
  });

  const discordChannel = await prisma.notificationChannel.upsert({
    where: { id: 'demo-discord-channel' },
    update: {},
    create: {
      id: 'demo-discord-channel',
      user_id: demoUser.id,
      channel_name: 'Demo Discord Webhook',
      channel_type: 'DISCORD',
      credentials: {
        webhook_url: 'https://discord.com/api/webhooks/your-webhook-url'
      },
      is_enabled: true
    }
  });

  console.log('✅ Created sample notification channels');

  // Create sample monitoring rules
  const gasMonitorRule = await prisma.monitoringRule.upsert({
    where: { id: 'demo-gas-monitor' },
    update: {},
    create: {
      id: 'demo-gas-monitor',
      user_id: demoUser.id,
      rule_name: 'Ethereum High Gas Alert',
      rule_type: 'GAS_PRICE',
      chain_id: 1, // Ethereum mainnet
      config: {
        condition: 'GREATER_THAN',
        threshold_gwei: 100
      },
      notification_channel_id: telegramChannel.id,
      notification_interval_seconds: 600,
      is_active: false // Disabled by default
    }
  });

  const walletMonitorRule = await prisma.monitoringRule.upsert({
    where: { id: 'demo-wallet-monitor' },
    update: {},
    create: {
      id: 'demo-wallet-monitor',
      user_id: demoUser.id,
      rule_name: 'Demo Wallet Monitor',
      rule_type: 'WALLET_TRANSFER',
      chain_id: 1, // Ethereum mainnet
      config: {
        addresses: ['******************************************'], // Example address
        monitor_type: ['TOKEN', 'NFT']
      },
      notification_channel_id: discordChannel.id,
      notification_interval_seconds: 300,
      is_active: false // Disabled by default
    }
  });

  console.log('✅ Created sample monitoring rules');

  // Seed exchanges
  const exchanges = [
    { name: 'Binance', display_name: 'Binance', api_base_url: 'https://api.binance.com' },
    { name: 'OKX', display_name: 'OKX', api_base_url: 'https://www.okx.com' },
    { name: 'Bybit', display_name: 'Bybit', api_base_url: 'https://api.bybit.com' },
    { name: 'Bitget', display_name: 'Bitget', api_base_url: 'https://api.bitget.com' },
  ];

  for (const exchangeData of exchanges) {
    const exchange = await prisma.exchange.upsert({
      where: { name: exchangeData.name },
      update: exchangeData,
      create: exchangeData,
    });
    console.log('✅ Created/updated exchange:', exchange.name);
  }

  // Seed popular tokens
  const tokens = [
    { symbol: 'USDT', name: 'Tether USD' },
    { symbol: 'BTC', name: 'Bitcoin' },
    { symbol: 'ETH', name: 'Ethereum' },
    { symbol: 'BNB', name: 'BNB' },
    { symbol: 'USDC', name: 'USD Coin' },
    { symbol: 'XRP', name: 'Ripple' },
    { symbol: 'ADA', name: 'Cardano' },
    { symbol: 'DOGE', name: 'Dogecoin' },
    { symbol: 'SOL', name: 'Solana' },
    { symbol: 'TRX', name: 'TRON' },
  ];

  for (const tokenData of tokens) {
    const token = await prisma.token.upsert({
      where: { symbol: tokenData.symbol },
      update: tokenData,
      create: tokenData,
    });
    console.log('✅ Created/updated token:', token.symbol);
  }

  // Seed networks
  const networks = [
    { name: 'ERC20', display_name: 'Ethereum (ERC20)', chain_id: 1 },
    { name: 'TRC20', display_name: 'TRON (TRC20)', chain_id: null },
    { name: 'BSC', display_name: 'BNB Smart Chain (BSC)', chain_id: 56 },
    { name: 'Polygon', display_name: 'Polygon (MATIC)', chain_id: 137 },
    { name: 'Arbitrum', display_name: 'Arbitrum One', chain_id: 42161 },
    { name: 'Optimism', display_name: 'Optimism', chain_id: 10 },
    { name: 'Avalanche', display_name: 'Avalanche C-Chain', chain_id: 43114 },
    { name: 'Solana', display_name: 'Solana', chain_id: null },
    { name: 'Bitcoin', display_name: 'Bitcoin', chain_id: null },
  ];

  for (const networkData of networks) {
    const network = await prisma.network.upsert({
      where: { name: networkData.name },
      update: networkData,
      create: networkData,
    });
    console.log('✅ Created/updated network:', network.name);
  }

  console.log('🎉 Database seeding completed successfully!');
  console.log('\n📋 Demo User Credentials:');
  console.log(`Username: demo`);
  console.log(`Password: demo123456`);
  console.log(`API Key: ${demoUser.api_key}`);
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
