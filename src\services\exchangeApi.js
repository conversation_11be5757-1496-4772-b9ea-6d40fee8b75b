const axios = require('axios');
const logger = require('../utils/logger');

/**
 * Base Exchange API Service
 * Abstract class for exchange API implementations
 */
class BaseExchangeService {
  constructor(name, baseUrl, options = {}) {
    this.name = name;
    this.baseUrl = baseUrl;
    this.timeout = options.timeout || 10000;
    this.retryAttempts = options.retryAttempts || 3;
    this.retryDelay = options.retryDelay || 1000;
    
    this.client = axios.create({
      baseURL: this.baseUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'DDDDAlert/1.0'
      }
    });
  }

  /**
   * Make HTTP request with retry logic
   */
  async makeRequest(config, attempt = 1) {
    try {
      const response = await this.client(config);
      return response.data;
    } catch (error) {
      logger.error(`${this.name} API request failed (attempt ${attempt}):`, {
        url: config.url,
        method: config.method,
        error: error.message,
        status: error.response?.status,
        data: error.response?.data
      });

      if (attempt < this.retryAttempts && this.shouldRetry(error)) {
        await this.delay(this.retryDelay * attempt);
        return this.makeRequest(config, attempt + 1);
      }

      throw error;
    }
  }

  /**
   * Determine if request should be retried
   */
  shouldRetry(error) {
    if (!error.response) return true; // Network error
    const status = error.response.status;
    return status >= 500 || status === 429; // Server error or rate limit
  }

  /**
   * Delay utility
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Abstract method - must be implemented by subclasses
   * Should return array of deposit/withdraw records
   */
  async fetchDepositWithdrawInfo() {
    throw new Error('fetchDepositWithdrawInfo must be implemented by subclass');
  }

  /**
   * Normalize currency symbol (remove common suffixes/prefixes)
   */
  normalizeCurrency(symbol) {
    if (!symbol) return symbol;
    
    // Remove common suffixes
    const suffixes = ['USDT', 'BTC', 'ETH', 'BNB'];
    const prefixes = ['X', 'Z'];
    
    let normalized = symbol.toUpperCase();
    
    // Handle special cases
    const specialCases = {
      'XBTC': 'BTC',
      'XETH': 'ETH',
      'ZUSD': 'USD'
    };
    
    return specialCases[normalized] || normalized;
  }

  /**
   * Normalize network name
   */
  normalizeNetwork(network) {
    if (!network) return network;
    
    const networkMap = {
      'ETH': 'ERC20',
      'ETHEREUM': 'ERC20',
      'ERC-20': 'ERC20',
      'TRX': 'TRC20',
      'TRON': 'TRC20',
      'TRC-20': 'TRC20',
      'BEP20': 'BSC',
      'BNB': 'BSC',
      'BNBSMARTCHAIN': 'BSC',
      'MATIC': 'Polygon',
      'POLYGON': 'Polygon',
      'ARBITRUM': 'Arbitrum',
      'ARB': 'Arbitrum',
      'OPTIMISM': 'Optimism',
      'OP': 'Optimism',
      'AVAX': 'Avalanche',
      'AVALANCHE': 'Avalanche',
      'SOL': 'Solana',
      'SOLANA': 'Solana',
      'BTC': 'Bitcoin',
      'BITCOIN': 'Bitcoin'
    };
    
    return networkMap[network.toUpperCase()] || network;
  }
}

/**
 * Binance API Service
 */
class BinanceService extends BaseExchangeService {
  constructor() {
    super('Binance', 'https://api.binance.com');
  }

  async fetchDepositWithdrawInfo() {
    try {
      logger.info('Fetching Binance deposit/withdraw info...');
      
      // Binance public API endpoint for coin info
      const response = await this.makeRequest({
        method: 'GET',
        url: '/sapi/v1/capital/config/getall'
      });

      const records = [];
      
      if (Array.isArray(response)) {
        for (const coin of response) {
          for (const network of coin.networkList || []) {
            records.push({
              exchange: 'Binance',
              token: this.normalizeCurrency(coin.coin),
              network: this.normalizeNetwork(network.network),
              can_deposit: network.depositEnable === true,
              can_withdraw: network.withdrawEnable === true,
              deposit_fee: network.depositFee || '0',
              withdrawal_fee: network.withdrawFee || '0',
              min_deposit: network.depositMin || '0',
              min_withdrawal: network.withdrawMin || '0',
              deposit_confirmations: network.minConfirm || 0,
              withdrawal_confirmations: network.unLockConfirm || 0,
              status_notes: this.getStatusNotes(network)
            });
          }
        }
      }

      logger.info(`Fetched ${records.length} Binance records`);
      return records;
    } catch (error) {
      logger.error('Failed to fetch Binance data:', error.message);
      return [];
    }
  }

  getStatusNotes(network) {
    const notes = [];
    if (!network.depositEnable) notes.push('充值暂停');
    if (!network.withdrawEnable) notes.push('提现暂停');
    if (network.specialTips) notes.push(network.specialTips);
    return notes.length > 0 ? notes.join('; ') : null;
  }
}

/**
 * OKX API Service
 */
class OKXService extends BaseExchangeService {
  constructor() {
    super('OKX', 'https://www.okx.com');
  }

  async fetchDepositWithdrawInfo() {
    try {
      logger.info('Fetching OKX deposit/withdraw info...');
      
      // OKX public API endpoint for currencies
      const response = await this.makeRequest({
        method: 'GET',
        url: '/api/v5/asset/currencies'
      });

      const records = [];
      
      if (response.code === '0' && Array.isArray(response.data)) {
        for (const currency of response.data) {
          records.push({
            exchange: 'OKX',
            token: this.normalizeCurrency(currency.ccy),
            network: this.normalizeNetwork(currency.chain),
            can_deposit: currency.canDep === true,
            can_withdraw: currency.canWd === true,
            deposit_fee: '0', // OKX typically doesn't charge deposit fees
            withdrawal_fee: currency.minFee || '0',
            min_deposit: currency.minDep || '0',
            min_withdrawal: currency.minWd || '0',
            deposit_confirmations: parseInt(currency.minDepArrivalConfirm) || 0,
            withdrawal_confirmations: parseInt(currency.minWdUnlockConfirm) || 0,
            status_notes: this.getStatusNotes(currency)
          });
        }
      }

      logger.info(`Fetched ${records.length} OKX records`);
      return records;
    } catch (error) {
      logger.error('Failed to fetch OKX data:', error.message);
      return [];
    }
  }

  getStatusNotes(currency) {
    const notes = [];
    if (!currency.canDep) notes.push('充值暂停');
    if (!currency.canWd) notes.push('提现暂停');
    if (currency.depQuotaFixed && parseFloat(currency.depQuotaFixed) > 0) {
      notes.push(`充值限额: ${currency.depQuotaFixed}`);
    }
    if (currency.wdQuota && parseFloat(currency.wdQuota) > 0) {
      notes.push(`提现限额: ${currency.wdQuota}`);
    }
    return notes.length > 0 ? notes.join('; ') : null;
  }
}

/**
 * Bybit API Service
 */
class BybitService extends BaseExchangeService {
  constructor() {
    super('Bybit', 'https://api.bybit.com');
  }

  async fetchDepositWithdrawInfo() {
    try {
      logger.info('Fetching Bybit deposit/withdraw info...');
      
      // Bybit public API endpoint for coin info
      const response = await this.makeRequest({
        method: 'GET',
        url: '/v5/asset/coin/query-info'
      });

      const records = [];
      
      if (response.retCode === 0 && response.result?.rows) {
        for (const coin of response.result.rows) {
          for (const chain of coin.chains || []) {
            records.push({
              exchange: 'Bybit',
              token: this.normalizeCurrency(coin.name),
              network: this.normalizeNetwork(chain.chain),
              can_deposit: chain.chainDeposit === '1',
              can_withdraw: chain.chainWithdraw === '1',
              deposit_fee: '0', // Bybit typically doesn't charge deposit fees
              withdrawal_fee: chain.withdrawFee || '0',
              min_deposit: chain.minDepositAmount || '0',
              min_withdrawal: chain.minWithdrawAmount || '0',
              deposit_confirmations: parseInt(chain.confirmations) || 0,
              withdrawal_confirmations: parseInt(chain.confirmations) || 0,
              status_notes: this.getStatusNotes(chain)
            });
          }
        }
      }

      logger.info(`Fetched ${records.length} Bybit records`);
      return records;
    } catch (error) {
      logger.error('Failed to fetch Bybit data:', error.message);
      return [];
    }
  }

  getStatusNotes(chain) {
    const notes = [];
    if (chain.chainDeposit !== '1') notes.push('充值暂停');
    if (chain.chainWithdraw !== '1') notes.push('提现暂停');
    return notes.length > 0 ? notes.join('; ') : null;
  }
}

/**
 * Bitget API Service
 */
class BitgetService extends BaseExchangeService {
  constructor() {
    super('Bitget', 'https://api.bitget.com');
  }

  async fetchDepositWithdrawInfo() {
    try {
      logger.info('Fetching Bitget deposit/withdraw info...');

      // Bitget public API endpoint for coin info
      const response = await this.makeRequest({
        method: 'GET',
        url: '/api/spot/v1/public/currencies'
      });

      const records = [];

      if (response.code === '00000' && Array.isArray(response.data)) {
        for (const currency of response.data) {
          for (const chain of currency.chains || []) {
            records.push({
              exchange: 'Bitget',
              token: this.normalizeCurrency(currency.coinName),
              network: this.normalizeNetwork(chain.chain),
              can_deposit: chain.rechargeable === true,
              can_withdraw: chain.withdrawable === true,
              deposit_fee: '0', // Bitget typically doesn't charge deposit fees
              withdrawal_fee: chain.withdrawFee || '0',
              min_deposit: chain.minDepositAmount || '0',
              min_withdrawal: chain.minWithdrawAmount || '0',
              deposit_confirmations: parseInt(chain.confirmation) || 0,
              withdrawal_confirmations: parseInt(chain.confirmation) || 0,
              status_notes: this.getStatusNotes(chain)
            });
          }
        }
      }

      logger.info(`Fetched ${records.length} Bitget records`);
      return records;
    } catch (error) {
      logger.error('Failed to fetch Bitget data:', error.message);
      return [];
    }
  }

  getStatusNotes(chain) {
    const notes = [];
    if (!chain.rechargeable) notes.push('充值暂停');
    if (!chain.withdrawable) notes.push('提现暂停');
    if (chain.tips) notes.push(chain.tips);
    return notes.length > 0 ? notes.join('; ') : null;
  }
}

/**
 * Exchange Manager - manages all exchange services
 */
class ExchangeManager {
  constructor() {
    this.exchanges = new Map();
    this.initializeExchanges();
  }

  initializeExchanges() {
    this.exchanges.set('Binance', new BinanceService());
    this.exchanges.set('OKX', new OKXService());
    this.exchanges.set('Bybit', new BybitService());
    this.exchanges.set('Bitget', new BitgetService());
  }

  /**
   * Get all exchange services
   */
  getAllExchanges() {
    return Array.from(this.exchanges.values());
  }

  /**
   * Get specific exchange service
   */
  getExchange(name) {
    return this.exchanges.get(name);
  }

  /**
   * Fetch data from all exchanges
   */
  async fetchAllExchangeData() {
    const allRecords = [];
    const errors = [];

    for (const [name, service] of this.exchanges) {
      try {
        const records = await service.fetchDepositWithdrawInfo();
        allRecords.push(...records);
        logger.info(`Successfully fetched ${records.length} records from ${name}`);
      } catch (error) {
        logger.error(`Failed to fetch data from ${name}:`, error.message);
        errors.push({ exchange: name, error: error.message });
      }
    }

    logger.info(`Total records fetched: ${allRecords.length}`);
    if (errors.length > 0) {
      logger.warn(`Errors occurred for ${errors.length} exchanges:`, errors);
    }

    return {
      records: allRecords,
      errors: errors,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Fetch data from specific exchange
   */
  async fetchExchangeData(exchangeName) {
    const service = this.getExchange(exchangeName);
    if (!service) {
      throw new Error(`Exchange ${exchangeName} not found`);
    }

    try {
      const records = await service.fetchDepositWithdrawInfo();
      return {
        records: records,
        errors: [],
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error(`Failed to fetch data from ${exchangeName}:`, error.message);
      return {
        records: [],
        errors: [{ exchange: exchangeName, error: error.message }],
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Create singleton instance
const exchangeManager = new ExchangeManager();

module.exports = {
  BaseExchangeService,
  BinanceService,
  OKXService,
  BybitService,
  BitgetService,
  ExchangeManager,
  exchangeManager
};
