# 交易所充提币模块

## 概述

交易所充提币模块是 DDDDAlert 后端项目的新增功能，用于自动收集和提供各大交易所的充值提币状态信息。该模块每15分钟自动访问 Binance、OKX、Bybit、Bitget 等主流交易所的公开API，获取完整的充提币信息并保存到数据库中，对外提供查询接口。

## 功能特性

### 🔄 自动数据收集
- **定时任务**: 每15分钟自动执行数据收集
- **多交易所支持**: Binance、OKX、Bybit、Bitget
- **容错机制**: 单个交易所失败不影响其他交易所数据收集
- **重试逻辑**: 网络错误和服务器错误自动重试

### 📊 数据管理
- **数据标准化**: 统一不同交易所的数据格式
- **增量更新**: 只更新变化的数据，提高效率
- **数据完整性**: 确保币种、网络、交易所信息的一致性

### 🔍 查询接口
- **无需认证**: 公开查询接口，无需身份验证
- **灵活筛选**: 支持按币种、网络、交易所筛选
- **实时状态**: 显示最后更新时间和数据新鲜度

## 技术架构

### 数据库设计

```sql
-- 交易所表
CREATE TABLE exchanges (
  id VARCHAR PRIMARY KEY,
  name VARCHAR UNIQUE,
  display_name VARCHAR,
  api_base_url VARCHAR,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);

-- 币种表
CREATE TABLE tokens (
  id VARCHAR PRIMARY KEY,
  symbol VARCHAR UNIQUE,
  name VARCHAR,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);

-- 网络表
CREATE TABLE networks (
  id VARCHAR PRIMARY KEY,
  name VARCHAR UNIQUE,
  display_name VARCHAR,
  chain_id INTEGER,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);

-- 充提币记录表
CREATE TABLE deposit_withdraw_records (
  id VARCHAR PRIMARY KEY,
  exchange_id VARCHAR REFERENCES exchanges(id),
  token_id VARCHAR REFERENCES tokens(id),
  network_id VARCHAR REFERENCES networks(id),
  can_deposit BOOLEAN DEFAULT false,
  can_withdraw BOOLEAN DEFAULT false,
  deposit_fee VARCHAR,
  withdrawal_fee VARCHAR,
  min_deposit VARCHAR,
  min_withdrawal VARCHAR,
  deposit_confirmations INTEGER,
  withdrawal_confirmations INTEGER,
  status_notes TEXT,
  last_updated_at TIMESTAMP,
  created_at TIMESTAMP,
  UNIQUE(exchange_id, token_id, network_id)
);
```

### 服务架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   定时调度器     │───▶│   数据收集器     │───▶│   数据存储器     │
│ ExchangeData    │    │ ExchangeAPI     │    │ ExchangeDB      │
│ Scheduler       │    │ Services        │    │ Service         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   任务队列       │    │   外部API       │    │   PostgreSQL    │
│   BullMQ        │    │   交易所APIs     │    │   数据库        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## API 接口

### 1. 获取筛选选项

**接口**: `GET /api/v1/exchange/options`

**响应示例**:
```json
{
  "tokens": [
    {"value": "USDT", "label": "USDT"},
    {"value": "BTC", "label": "BTC"},
    {"value": "ETH", "label": "ETH"}
  ],
  "chains": [
    {"value": "ALL", "label": "所有链 (All Chains)"},
    {"value": "ERC20", "label": "Ethereum (ERC20)"},
    {"value": "TRC20", "label": "TRON (TRC20)"}
  ],
  "exchanges": [
    {"value": "ALL", "label": "所有交易所 (All Exchanges)"},
    {"value": "Binance", "label": "Binance"},
    {"value": "OKX", "label": "OKX"}
  ]
}
```

### 2. 查询充提状态

**接口**: `GET /api/v1/exchange/status`

**请求参数**:
- `token` (必需): 币种符号，如 USDT、BTC、ETH
- `chain` (可选): 区块链网络，默认为 ALL
- `exchange` (可选): 交易所名称，默认为 ALL

**请求示例**:
```
GET /api/v1/exchange/status?token=USDT&chain=ERC20&exchange=Binance
```

**响应示例**:
```json
{
  "data": [
    {
      "exchange": "Binance",
      "token": "USDT",
      "chain": "ERC20",
      "can_deposit": true,
      "can_withdraw": false,
      "deposit_fee": "0",
      "withdrawal_fee": "1.3",
      "min_deposit": "10.0",
      "min_withdrawal": "20.0",
      "deposit_confirmations": 12,
      "withdrawal_confirmations": 12,
      "status_notes": "提现钱包正在维护"
    }
  ],
  "last_updated_timestamp": "2025-01-27T10:30:00Z",
  "total_records": 1
}
```

### 3. 系统统计信息

**接口**: `GET /api/v1/exchange/statistics`

**响应示例**:
```json
{
  "totalRecords": 1000,
  "activeExchanges": 4,
  "activeTokens": 50,
  "activeNetworks": 9,
  "lastUpdate": "2025-01-27T10:30:00Z",
  "systemStatus": {
    "isInitialized": true,
    "worker": {"isRunning": true}
  }
}
```

### 4. 健康检查

**接口**: `GET /api/v1/exchange/health`

**响应示例**:
```json
{
  "healthy": true,
  "status": {"isInitialized": true},
  "lastUpdate": "2025-01-27T10:30:00Z",
  "dataStale": false,
  "timestamp": "2025-01-27T10:35:00Z"
}
```

### 5. 手动刷新数据

**接口**: `POST /api/v1/exchange/refresh`

**响应示例**:
```json
{
  "success": true,
  "message": "Data refresh triggered successfully",
  "job_id": "job-123456"
}
```

**单个交易所刷新**: `POST /api/v1/exchange/refresh/Binance`

## 部署说明

### 环境要求

- Node.js 16+
- PostgreSQL 12+
- Redis 6+

### 安装步骤

1. **数据库迁移**:
```bash
npm run db:push
```

2. **种子数据**:
```bash
npm run db:seed
```

3. **启动服务**:
```bash
npm start
```

### 环境变量

```env
# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/ddddalert

# Redis配置
REDIS_URL=redis://localhost:6379

# 其他现有配置...
```

## 监控和维护

### 日志监控

系统会记录以下关键日志：
- 数据收集成功/失败
- API调用错误
- 数据库操作异常
- 任务队列状态

### 性能指标

- 数据收集频率：每15分钟
- API响应时间：< 500ms
- 数据新鲜度：< 30分钟
- 系统可用性：> 99%

### 故障处理

1. **单个交易所API失败**: 不影响其他交易所数据收集
2. **数据库连接失败**: 任务会重试，失败后记录错误日志
3. **Redis连接失败**: 定时任务停止，但查询接口仍可用
4. **数据过期**: 健康检查会标记为不健康状态

## 扩展功能

### 未来规划

1. **更多交易所**: 支持更多主流交易所
2. **实时推送**: WebSocket实时数据推送
3. **历史数据**: 充提币状态历史记录
4. **告警功能**: 充提币状态变化告警
5. **数据分析**: 充提币费用趋势分析

### 自定义开发

可以通过继承 `BaseExchangeService` 类来添加新的交易所支持：

```javascript
class NewExchangeService extends BaseExchangeService {
  constructor() {
    super('NewExchange', 'https://api.newexchange.com');
  }

  async fetchDepositWithdrawInfo() {
    // 实现具体的API调用逻辑
  }
}
```

## 测试

运行测试套件：

```bash
# 运行所有测试
npm test

# 运行交易所模块测试
npm test -- tests/exchange*.test.js

# 运行测试并生成覆盖率报告
npm test -- --coverage
```

## 支持

如有问题或建议，请联系开发团队或提交 Issue。
