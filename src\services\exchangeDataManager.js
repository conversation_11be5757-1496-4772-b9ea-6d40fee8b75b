const { ExchangeDataWorker, ExchangeDataScheduler } = require('../workers/exchangeDataWorker');
const { exchangeDatabase } = require('./exchangeDatabase');
const logger = require('../utils/logger');

/**
 * Exchange Data Manager
 * Manages the entire exchange data collection and processing system
 */
class ExchangeDataManager {
  constructor(queueService) {
    this.queueService = queueService;
    this.worker = null;
    this.scheduler = null;
    this.isInitialized = false;
  }

  /**
   * Initialize the exchange data management system
   */
  async initialize() {
    try {
      if (this.isInitialized) {
        logger.warn('Exchange data manager already initialized');
        return;
      }

      logger.info('Initializing exchange data management system...');

      // Initialize worker
      this.worker = new ExchangeDataWorker(this.queueService);
      await this.worker.initialize();

      // Initialize scheduler
      this.scheduler = new ExchangeDataScheduler(this.queueService);

      // Schedule regular data fetching (every 15 minutes)
      await this.scheduler.scheduleRegularFetch();

      // Trigger immediate initial fetch
      await this.scheduler.scheduleImmediateFetch();

      this.isInitialized = true;
      logger.info('Exchange data management system initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize exchange data manager:', error.message);
      throw error;
    }
  }

  /**
   * Shutdown the exchange data management system
   */
  async shutdown() {
    try {
      if (!this.isInitialized) {
        logger.warn('Exchange data manager not initialized');
        return;
      }

      logger.info('Shutting down exchange data management system...');

      // Cancel scheduled jobs
      if (this.scheduler) {
        await this.scheduler.cancelScheduledJobs();
      }

      // Shutdown worker
      if (this.worker) {
        await this.worker.shutdown();
      }

      this.isInitialized = false;
      logger.info('Exchange data management system shut down successfully');
    } catch (error) {
      logger.error('Failed to shutdown exchange data manager:', error.message);
      throw error;
    }
  }

  /**
   * Trigger immediate data fetch for all exchanges
   */
  async triggerImmediateFetch() {
    try {
      if (!this.isInitialized) {
        throw new Error('Exchange data manager not initialized');
      }

      logger.info('Triggering immediate data fetch for all exchanges...');
      const job = await this.scheduler.scheduleImmediateFetch();
      
      return {
        success: true,
        jobId: job.id,
        message: 'Immediate fetch job scheduled successfully'
      };
    } catch (error) {
      logger.error('Failed to trigger immediate fetch:', error.message);
      throw error;
    }
  }

  /**
   * Trigger immediate data fetch for specific exchange
   */
  async triggerExchangeFetch(exchangeName) {
    try {
      if (!this.isInitialized) {
        throw new Error('Exchange data manager not initialized');
      }

      logger.info(`Triggering immediate data fetch for ${exchangeName}...`);
      const job = await this.scheduler.scheduleImmediateFetch(exchangeName);
      
      return {
        success: true,
        jobId: job.id,
        exchange: exchangeName,
        message: `Immediate fetch job for ${exchangeName} scheduled successfully`
      };
    } catch (error) {
      logger.error(`Failed to trigger fetch for ${exchangeName}:`, error.message);
      throw error;
    }
  }

  /**
   * Get system status
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      worker: this.worker ? this.worker.getStatus() : null,
      scheduledJobs: this.scheduler ? this.scheduler.getScheduledJobs() : []
    };
  }

  /**
   * Query deposit/withdraw records
   */
  async queryRecords(filters = {}) {
    try {
      return await exchangeDatabase.queryRecords(filters);
    } catch (error) {
      logger.error('Failed to query records:', error.message);
      throw error;
    }
  }

  /**
   * Get filter options for frontend
   */
  async getFilterOptions() {
    try {
      return await exchangeDatabase.getFilterOptions();
    } catch (error) {
      logger.error('Failed to get filter options:', error.message);
      throw error;
    }
  }

  /**
   * Get last update timestamp
   */
  async getLastUpdateTimestamp() {
    try {
      return await exchangeDatabase.getLastUpdateTimestamp();
    } catch (error) {
      logger.error('Failed to get last update timestamp:', error.message);
      return null;
    }
  }

  /**
   * Get statistics
   */
  async getStatistics() {
    try {
      const [
        totalRecords,
        activeExchanges,
        activeTokens,
        activeNetworks,
        lastUpdate
      ] = await Promise.all([
        exchangeDatabase.prisma.depositWithdrawRecord.count(),
        exchangeDatabase.prisma.exchange.count({ where: { is_active: true } }),
        exchangeDatabase.prisma.token.count({ where: { is_active: true } }),
        exchangeDatabase.prisma.network.count({ where: { is_active: true } }),
        this.getLastUpdateTimestamp()
      ]);

      return {
        totalRecords,
        activeExchanges,
        activeTokens,
        activeNetworks,
        lastUpdate,
        systemStatus: this.getStatus()
      };
    } catch (error) {
      logger.error('Failed to get statistics:', error.message);
      throw error;
    }
  }

  /**
   * Health check
   */
  async healthCheck() {
    try {
      const status = this.getStatus();
      const lastUpdate = await this.getLastUpdateTimestamp();
      
      // Check if data is stale (older than 30 minutes)
      const isDataStale = lastUpdate ? 
        (Date.now() - new Date(lastUpdate).getTime()) > 30 * 60 * 1000 : true;

      return {
        healthy: status.isInitialized && status.worker?.isRunning && !isDataStale,
        status: status,
        lastUpdate: lastUpdate,
        dataStale: isDataStale,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Health check failed:', error.message);
      return {
        healthy: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

module.exports = {
  ExchangeDataManager
};
