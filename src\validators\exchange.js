const Joi = require('joi');
const logger = require('../utils/logger');

/**
 * Validation schema for exchange query parameters
 */
const exchangeQuerySchema = Joi.object({
  token: Joi.string()
    .trim()
    .uppercase()
    .min(1)
    .max(20)
    .pattern(/^[A-Z0-9]+$/)
    .required()
    .messages({
      'string.empty': 'Token symbol is required',
      'string.min': 'Token symbol must be at least 1 character',
      'string.max': 'Token symbol must not exceed 20 characters',
      'string.pattern.base': 'Token symbol must contain only uppercase letters and numbers',
      'any.required': 'Token parameter is required'
    }),
    
  chain: Joi.string()
    .trim()
    .valid('ALL', 'ERC20', 'TRC20', 'BSC', 'Polygon', 'Arbitrum', 'Optimism', 'Avalanche', 'Solana', 'Bitcoin')
    .default('ALL')
    .messages({
      'any.only': 'Chain must be one of: ALL, ERC20, TRC20, BSC, Polygon, Arbitrum, Optimism, Avalanche, Solana, Bitcoin'
    }),
    
  exchange: Joi.string()
    .trim()
    .valid('ALL', 'Binance', 'OKX', 'Bybit', 'Bitget')
    .default('ALL')
    .messages({
      'any.only': 'Exchange must be one of: ALL, Binance, OKX, Bybit, Bitget'
    })
});

/**
 * Validation schema for exchange refresh parameters
 */
const exchangeRefreshSchema = Joi.object({
  exchangeName: Joi.string()
    .trim()
    .valid('Binance', 'OKX', 'Bybit', 'Bitget')
    .required()
    .messages({
      'any.only': 'Exchange name must be one of: Binance, OKX, Bybit, Bitget',
      'any.required': 'Exchange name is required'
    })
});

/**
 * Middleware to validate exchange query parameters
 */
function validateExchangeQuery(req, res, next) {
  try {
    const { error, value } = exchangeQuerySchema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errorMessages = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      logger.warn('Exchange query validation failed:', errorMessages);

      return res.status(400).json({
        error: 'Validation error',
        message: 'Invalid query parameters',
        details: errorMessages
      });
    }

    // Replace query with validated values
    req.query = value;
    next();
  } catch (err) {
    logger.error('Exchange query validation error:', err.message);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Validation processing failed'
    });
  }
}

/**
 * Middleware to validate exchange refresh parameters
 */
function validateExchangeRefresh(req, res, next) {
  try {
    const { error, value } = exchangeRefreshSchema.validate(req.params, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errorMessages = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      logger.warn('Exchange refresh validation failed:', errorMessages);

      return res.status(400).json({
        error: 'Validation error',
        message: 'Invalid parameters',
        details: errorMessages
      });
    }

    // Replace params with validated values
    req.params = { ...req.params, ...value };
    next();
  } catch (err) {
    logger.error('Exchange refresh validation error:', err.message);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Validation processing failed'
    });
  }
}

/**
 * Validate token symbol format
 */
function isValidTokenSymbol(symbol) {
  if (!symbol || typeof symbol !== 'string') {
    return false;
  }
  
  return /^[A-Z0-9]{1,20}$/.test(symbol.trim().toUpperCase());
}

/**
 * Validate chain/network name
 */
function isValidChainName(chain) {
  if (!chain || typeof chain !== 'string') {
    return false;
  }
  
  const validChains = ['ALL', 'ERC20', 'TRC20', 'BSC', 'Polygon', 'Arbitrum', 'Optimism', 'Avalanche', 'Solana', 'Bitcoin'];
  return validChains.includes(chain.trim());
}

/**
 * Validate exchange name
 */
function isValidExchangeName(exchange) {
  if (!exchange || typeof exchange !== 'string') {
    return false;
  }
  
  const validExchanges = ['ALL', 'Binance', 'OKX', 'Bybit', 'Bitget'];
  return validExchanges.includes(exchange.trim());
}

/**
 * Sanitize and normalize query parameters
 */
function sanitizeQueryParams(params) {
  const sanitized = {};
  
  if (params.token) {
    sanitized.token = params.token.toString().trim().toUpperCase();
  }
  
  if (params.chain) {
    sanitized.chain = params.chain.toString().trim();
  }
  
  if (params.exchange) {
    sanitized.exchange = params.exchange.toString().trim();
  }
  
  return sanitized;
}

/**
 * Get validation error response
 */
function getValidationErrorResponse(field, message) {
  return {
    error: 'Validation error',
    message: `Invalid ${field}`,
    details: [{
      field: field,
      message: message
    }]
  };
}

module.exports = {
  exchangeQuerySchema,
  exchangeRefreshSchema,
  validateExchangeQuery,
  validateExchangeRefresh,
  isValidTokenSymbol,
  isValidChainName,
  isValidExchangeName,
  sanitizeQueryParams,
  getValidationErrorResponse
};
