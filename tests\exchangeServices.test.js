const axios = require('axios');
const { 
  BinanceService, 
  OKXService, 
  BybitService, 
  BitgetService,
  ExchangeManager 
} = require('../src/services/exchangeApi');

// Mock axios
jest.mock('axios');
const mockedAxios = axios;

describe('Exchange API Services', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('BinanceService', () => {
    let binanceService;

    beforeEach(() => {
      binanceService = new BinanceService();
    });

    it('should initialize with correct configuration', () => {
      expect(binanceService.name).toBe('Binance');
      expect(binanceService.baseUrl).toBe('https://api.binance.com');
    });

    it('should fetch deposit/withdraw info successfully', async () => {
      const mockResponse = [
        {
          coin: 'USDT',
          networkList: [
            {
              network: 'ETH',
              depositEnable: true,
              withdrawEnable: true,
              depositFee: '0',
              withdrawFee: '1.0',
              depositMin: '10.0',
              withdrawMin: '20.0',
              minConfirm: 12,
              unLockConfirm: 12
            }
          ]
        }
      ];

      mockedAxios.create.mockReturnValue({
        request: jest.fn().mockResolvedValue({ data: mockResponse })
      });

      const result = await binanceService.fetchDepositWithdrawInfo();

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        exchange: 'Binance',
        token: 'USDT',
        network: 'ERC20',
        can_deposit: true,
        can_withdraw: true
      });
    });

    it('should handle API errors gracefully', async () => {
      mockedAxios.create.mockReturnValue({
        request: jest.fn().mockRejectedValue(new Error('API Error'))
      });

      const result = await binanceService.fetchDepositWithdrawInfo();

      expect(result).toEqual([]);
    });

    it('should normalize currency symbols correctly', () => {
      expect(binanceService.normalizeCurrency('XBTC')).toBe('BTC');
      expect(binanceService.normalizeCurrency('USDT')).toBe('USDT');
      expect(binanceService.normalizeCurrency('eth')).toBe('ETH');
    });

    it('should normalize network names correctly', () => {
      expect(binanceService.normalizeNetwork('ETH')).toBe('ERC20');
      expect(binanceService.normalizeNetwork('TRX')).toBe('TRC20');
      expect(binanceService.normalizeNetwork('BNB')).toBe('BSC');
    });
  });

  describe('OKXService', () => {
    let okxService;

    beforeEach(() => {
      okxService = new OKXService();
    });

    it('should initialize with correct configuration', () => {
      expect(okxService.name).toBe('OKX');
      expect(okxService.baseUrl).toBe('https://www.okx.com');
    });

    it('should fetch deposit/withdraw info successfully', async () => {
      const mockResponse = {
        code: '0',
        data: [
          {
            ccy: 'USDT',
            chain: 'ETH',
            canDep: true,
            canWd: true,
            minFee: '1.0',
            minDep: '10.0',
            minWd: '20.0',
            minDepArrivalConfirm: '12',
            minWdUnlockConfirm: '12'
          }
        ]
      };

      mockedAxios.create.mockReturnValue({
        request: jest.fn().mockResolvedValue({ data: mockResponse })
      });

      const result = await okxService.fetchDepositWithdrawInfo();

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        exchange: 'OKX',
        token: 'USDT',
        network: 'ERC20',
        can_deposit: true,
        can_withdraw: true
      });
    });
  });

  describe('BybitService', () => {
    let bybitService;

    beforeEach(() => {
      bybitService = new BybitService();
    });

    it('should initialize with correct configuration', () => {
      expect(bybitService.name).toBe('Bybit');
      expect(bybitService.baseUrl).toBe('https://api.bybit.com');
    });

    it('should fetch deposit/withdraw info successfully', async () => {
      const mockResponse = {
        retCode: 0,
        result: {
          rows: [
            {
              name: 'USDT',
              chains: [
                {
                  chain: 'ETH',
                  chainDeposit: '1',
                  chainWithdraw: '1',
                  withdrawFee: '1.0',
                  minDepositAmount: '10.0',
                  minWithdrawAmount: '20.0',
                  confirmations: '12'
                }
              ]
            }
          ]
        }
      };

      mockedAxios.create.mockReturnValue({
        request: jest.fn().mockResolvedValue({ data: mockResponse })
      });

      const result = await bybitService.fetchDepositWithdrawInfo();

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        exchange: 'Bybit',
        token: 'USDT',
        network: 'ERC20',
        can_deposit: true,
        can_withdraw: true
      });
    });
  });

  describe('BitgetService', () => {
    let bitgetService;

    beforeEach(() => {
      bitgetService = new BitgetService();
    });

    it('should initialize with correct configuration', () => {
      expect(bitgetService.name).toBe('Bitget');
      expect(bitgetService.baseUrl).toBe('https://api.bitget.com');
    });

    it('should fetch deposit/withdraw info successfully', async () => {
      const mockResponse = {
        code: '00000',
        data: [
          {
            coinName: 'USDT',
            chains: [
              {
                chain: 'ETH',
                rechargeable: true,
                withdrawable: true,
                withdrawFee: '1.0',
                minDepositAmount: '10.0',
                minWithdrawAmount: '20.0',
                confirmation: '12'
              }
            ]
          }
        ]
      };

      mockedAxios.create.mockReturnValue({
        request: jest.fn().mockResolvedValue({ data: mockResponse })
      });

      const result = await bitgetService.fetchDepositWithdrawInfo();

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        exchange: 'Bitget',
        token: 'USDT',
        network: 'ERC20',
        can_deposit: true,
        can_withdraw: true
      });
    });
  });

  describe('ExchangeManager', () => {
    let exchangeManager;

    beforeEach(() => {
      exchangeManager = new ExchangeManager();
    });

    it('should initialize all exchange services', () => {
      const exchanges = exchangeManager.getAllExchanges();
      expect(exchanges).toHaveLength(4);
      
      const exchangeNames = exchanges.map(e => e.name);
      expect(exchangeNames).toContain('Binance');
      expect(exchangeNames).toContain('OKX');
      expect(exchangeNames).toContain('Bybit');
      expect(exchangeNames).toContain('Bitget');
    });

    it('should get specific exchange service', () => {
      const binanceService = exchangeManager.getExchange('Binance');
      expect(binanceService).toBeInstanceOf(BinanceService);
      expect(binanceService.name).toBe('Binance');
    });

    it('should return undefined for non-existent exchange', () => {
      const service = exchangeManager.getExchange('NonExistent');
      expect(service).toBeUndefined();
    });

    it('should fetch data from all exchanges', async () => {
      // Mock all services to return data
      const mockRecord = {
        exchange: 'Test',
        token: 'USDT',
        network: 'ERC20',
        can_deposit: true,
        can_withdraw: true
      };

      // Mock each service's fetchDepositWithdrawInfo method
      jest.spyOn(BinanceService.prototype, 'fetchDepositWithdrawInfo')
        .mockResolvedValue([{ ...mockRecord, exchange: 'Binance' }]);
      jest.spyOn(OKXService.prototype, 'fetchDepositWithdrawInfo')
        .mockResolvedValue([{ ...mockRecord, exchange: 'OKX' }]);
      jest.spyOn(BybitService.prototype, 'fetchDepositWithdrawInfo')
        .mockResolvedValue([{ ...mockRecord, exchange: 'Bybit' }]);
      jest.spyOn(BitgetService.prototype, 'fetchDepositWithdrawInfo')
        .mockResolvedValue([{ ...mockRecord, exchange: 'Bitget' }]);

      const result = await exchangeManager.fetchAllExchangeData();

      expect(result.records).toHaveLength(4);
      expect(result.errors).toHaveLength(0);
      expect(result.timestamp).toBeDefined();
    });

    it('should handle errors from individual exchanges', async () => {
      // Mock some services to fail
      jest.spyOn(BinanceService.prototype, 'fetchDepositWithdrawInfo')
        .mockRejectedValue(new Error('Binance API Error'));
      jest.spyOn(OKXService.prototype, 'fetchDepositWithdrawInfo')
        .mockResolvedValue([]);
      jest.spyOn(BybitService.prototype, 'fetchDepositWithdrawInfo')
        .mockRejectedValue(new Error('Bybit API Error'));
      jest.spyOn(BitgetService.prototype, 'fetchDepositWithdrawInfo')
        .mockResolvedValue([]);

      const result = await exchangeManager.fetchAllExchangeData();

      expect(result.records).toHaveLength(0);
      expect(result.errors).toHaveLength(2);
      expect(result.errors[0].exchange).toBe('Binance');
      expect(result.errors[1].exchange).toBe('Bybit');
    });

    it('should fetch data from specific exchange', async () => {
      const mockRecord = {
        exchange: 'Binance',
        token: 'USDT',
        network: 'ERC20',
        can_deposit: true,
        can_withdraw: true
      };

      jest.spyOn(BinanceService.prototype, 'fetchDepositWithdrawInfo')
        .mockResolvedValue([mockRecord]);

      const result = await exchangeManager.fetchExchangeData('Binance');

      expect(result.records).toHaveLength(1);
      expect(result.records[0]).toEqual(mockRecord);
      expect(result.errors).toHaveLength(0);
    });

    it('should handle error for non-existent exchange', async () => {
      await expect(exchangeManager.fetchExchangeData('NonExistent'))
        .rejects.toThrow('Exchange NonExistent not found');
    });
  });

  describe('Base Exchange Service', () => {
    let service;

    beforeEach(() => {
      service = new BinanceService();
    });

    it('should implement retry logic', async () => {
      const mockError = {
        response: { status: 500 }
      };

      const mockAxiosInstance = {
        request: jest.fn()
          .mockRejectedValueOnce(mockError)
          .mockRejectedValueOnce(mockError)
          .mockResolvedValueOnce({ data: 'success' })
      };

      mockedAxios.create.mockReturnValue(mockAxiosInstance);

      const result = await service.makeRequest({ url: '/test' });

      expect(result).toBe('success');
      expect(mockAxiosInstance.request).toHaveBeenCalledTimes(3);
    });

    it('should not retry on client errors', async () => {
      const mockError = {
        response: { status: 400 }
      };

      const mockAxiosInstance = {
        request: jest.fn().mockRejectedValue(mockError)
      };

      mockedAxios.create.mockReturnValue(mockAxiosInstance);

      await expect(service.makeRequest({ url: '/test' }))
        .rejects.toEqual(mockError);

      expect(mockAxiosInstance.request).toHaveBeenCalledTimes(1);
    });

    it('should retry on network errors', async () => {
      const networkError = new Error('Network Error');

      const mockAxiosInstance = {
        request: jest.fn()
          .mockRejectedValueOnce(networkError)
          .mockResolvedValueOnce({ data: 'success' })
      };

      mockedAxios.create.mockReturnValue(mockAxiosInstance);

      const result = await service.makeRequest({ url: '/test' });

      expect(result).toBe('success');
      expect(mockAxiosInstance.request).toHaveBeenCalledTimes(2);
    });
  });
});
